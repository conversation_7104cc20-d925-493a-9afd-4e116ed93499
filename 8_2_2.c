﻿/*********************************************************************************
// This code is created by SimCoder Version 2025a15.147 for F28004x Hardware Target
//
// SimCoder is copyright by Powersim Inc., 2009-2021
//
// Date: August 02, 2025 15:20:59
**********************************************************************************/
#include	<math.h>
#include	"PS_bios.h"
#define	GetCurTime() PS_GetSysTimer()
#define	iif(a, b, c) ((a) ? (b) : (c))
typedef interrupt void (*ClaIntr)(void);
ClaIntr Cla1Task1 = 0;
ClaIntr Cla1Task2 = 0;
ClaIntr Cla1Task3 = 0;
ClaIntr Cla1Task4 = 0;
ClaIntr Cla1Task5 = 0;
ClaIntr Cla1Task6 = 0;
ClaIntr Cla1Task7 = 0;
ClaIntr Cla1Task8 = 0;




interrupt void Task();
void Task_1();

#ifdef _FLASH
#pragma DATA_SECTION(PSK_SysClk, "copysections")
#endif
const Uint16 PSK_SysClk = 100;  // MHz
extern	DefaultType	fGblVav;
extern	DefaultType	fGbltheta_V;
extern	DefaultType	fGblILd;
extern	DefaultType	fGblILq;
extern	DefaultType	fGblsIdc;
extern	DefaultType	fGblVcona;
extern	DefaultType	fGblVconb;
extern	DefaultType	fGblVconc;
extern	DefaultType	fGblUDELAY16;
extern	DefaultType	fGblUDELAY17;
extern	DefaultType	fGblVq;
extern	DefaultType	fGblVd;
extern	DefaultType	fGblVd1;
extern	DefaultType	fGblVd2;
extern	DefaultType	fGblUDELAY7;
extern	DefaultType	fGblVcos;
extern	DefaultType	fGblVsin;
extern	DefaultType	fGblVs_sin;
extern	DefaultType	fGblVs_cos;
extern	DefaultType	fGblUDELAY9;
extern	DefaultType	fGblVerr;
extern	DefaultType	fGblFreq;
extern	DefaultType	fGblVc;
extern	DefaultType	fGblUDELAY11;
extern	DefaultType	fGblsVdc;
extern	DefaultType	fGblUDELAY4;
extern	DefaultType	fGblIoqc1;
extern	DefaultType	fGblVon;


#define	PSM_VRefHiA		3.3		// ADC-A VREFHIA
#define	PSM_VRefHiB		3.3		// ADC-B VREFHIB
#define	PSM_VRefHiC		3.3		// ADC-C VREFHIC





PST_BufItem aGblSciOutBuf[32];
Uint16 aGblSciOutAllow[3] = {0,0,0};
Uint16 aGblSciOutCnt[3] = {0,0,0};
Uint16 nGblSciState = 0;
Uint16 aGblSciDateSetPt[3] = {1,1,1};
char* aGblSciInitStr = "\0016,1:PSM_Idc=10000\003\0016,2:PSM_theta=10000\003\0016,3:PSM_Freq=10000\003";
#define	PSC_SCI_SENDOUT_FLAG	0x2000
#define	PSC_SCI_INITIAL		0
#define	PSC_SCI_START		0x5000000
#define	PSC_SCI_PAUSE		0x1000000
#define	PSC_SCI_RESTART		0x2000000
#define	PSC_SCI_CONT_MODE	0x3000000
#define	PSC_SCI_SNAP_MODE	0x4000000
#define	PSC_SCI_CONT_START	1
#define	PSC_SCI_CONT_BEGIN	2
#define	PSC_SCI_CONT_SEND	3
#define	PSC_SCI_CONT_PAUSE	4
#define	PSC_SCI_SNAP_START	100
#define	PSC_SCI_SNAP_BEGIN	101
#define	PSC_SCI_SNAP_SEND	102
#define	PSC_SCI_SNAP_WAIT	103
#define	PSC_SCI_SNAP_PSEND	104
#define	PSC_SCI_SNAP_PWAIT	105
#define	PSC_SCI_SNAP_PAUSE	106
void _ProcSciInputItem(PST_BufItem* pItem)
{
	Uint16 i, nSeqNo = pItem->nSeqNo.bit.nSeqNo;
	switch (nSeqNo) {
	case 0:
		switch (pItem->data.dataInt32) {
		case PSC_SCI_INITIAL:
			for (i = 0; i < 3; i++) aGblSciOutAllow[i] = 0;
			PS_SciClearSendBuf();
			PS_SciSendInitStr(aGblSciInitStr);
			break;
		case PSC_SCI_PAUSE:
			PSM_DisableIntr();
			switch (nGblSciState) {
			case PSC_SCI_CONT_START:
			case PSC_SCI_CONT_SEND:
				PS_SciClearSendBuf();
				nGblSciState = PSC_SCI_CONT_PAUSE;
				break;
			case PSC_SCI_SNAP_SEND:
				nGblSciState = PSC_SCI_SNAP_PSEND;
				break;
			case PSC_SCI_SNAP_WAIT:
				nGblSciState = PSC_SCI_SNAP_PWAIT;
				break;
			default:
				break;
			}
			PSM_EnableIntr();
			break;
		case PSC_SCI_RESTART:
			PSM_DisableIntr();
			switch (nGblSciState) {
			case PSC_SCI_CONT_PAUSE:
				nGblSciState = PSC_SCI_CONT_START;
				break;
			case PSC_SCI_SNAP_PSEND:
			case PSC_SCI_SNAP_PWAIT:
			case PSC_SCI_SNAP_PAUSE:
				nGblSciState = PSC_SCI_SNAP_START;
				break;
			}
			PSM_EnableIntr();
			break;
		case PSC_SCI_CONT_MODE:
			nGblSciState = PSC_SCI_CONT_START;
			break;
		case PSC_SCI_SNAP_MODE:
			nGblSciState = PSC_SCI_SNAP_START;
			break;
		default:
			if (pItem->nSeqNo.bit.nCount == 0) {
				for (i = 0; i < 3; i++) aGblSciOutAllow[i] = 0;
			}
			for (i = 0; i < 4; i++) {
				int index = (pItem->data.dataInt32 >> (i * 8)) & 0xff;
				if ((index > 0) && (index <= 3))
					aGblSciOutAllow[index - 1] = PSC_SCI_SENDOUT_FLAG;
			}
			break;
		}
		break;
	}
}

void _ProcSciRestart(void)
{
	int i;
	PST_BufItem item;

	for (i = 0; i < 3; i++)
		aGblSciOutAllow[i] &= 0xff00;
	item.nSeqNo.all = 0;
	switch (nGblSciState++) {
	case PSC_SCI_CONT_BEGIN:
		PS_SciClearSendBuf();
		item.data.dataInt32 = 0;
		break;
	case PSC_SCI_SNAP_BEGIN:
		item.data.dataInt32 = 1;
		break;
	case PSC_SCI_SNAP_PWAIT:
		nGblSciState = PSC_SCI_SNAP_START;
	case PSC_SCI_SNAP_WAIT:
		item.data.dataInt32 = 255;
		break;
	}
	PS_SciSendItem(&item);
}

void _ProcSciWaitStart(void)
{
	PSM_DisableIntr();
	switch (nGblSciState) {
	case PSC_SCI_CONT_START:
		nGblSciState = PSC_SCI_CONT_BEGIN;
		break;
	case PSC_SCI_SNAP_START:
		nGblSciState = PSC_SCI_SNAP_BEGIN;
		break;
	default:
		break;
	}
	PSM_EnableIntr();
}

void _ProcSciOutput(int index, float fVal)
{
	PST_BufItem item;
	int ok = ((aGblSciOutAllow[index] & PSC_SCI_SENDOUT_FLAG) &&
		(++aGblSciOutCnt[index] >= aGblSciDateSetPt[index]));
	PSM_DisableIntr();
	switch (nGblSciState) {
	case PSC_SCI_CONT_BEGIN:
	case PSC_SCI_SNAP_BEGIN:
		_ProcSciRestart();
		break;
	case PSC_SCI_CONT_SEND:
		if (ok) {
			aGblSciOutCnt[index] = 0;
			item.nSeqNo.bit.nCount = aGblSciOutAllow[index];
			item.nSeqNo.bit.nSeqNo = index + 1;
			item.data.dataFloat = fVal;
			PS_SciSendItem(&item);
			aGblSciOutAllow[index]++;
			aGblSciOutAllow[index] &= ~0x100;
		}
		break;
	case PSC_SCI_SNAP_SEND:
	case PSC_SCI_SNAP_PSEND:
		if (ok) {
			aGblSciOutCnt[index] = 0;
			item.nSeqNo.bit.nCount = aGblSciOutAllow[index];
			item.nSeqNo.bit.nSeqNo = index + 1;
			item.data.dataFloat = fVal;
			if (!PS_SciSendItem(&item)) {
				nGblSciState++;
			} else {
				aGblSciOutAllow[index]++;
				aGblSciOutAllow[index] &= ~0x100;
			}
		}
		break;
	case PSC_SCI_SNAP_WAIT:
		if (PS_IsTxQueueEmpty()) {
			nGblSciState = PSC_SCI_SNAP_START;
		}
		break;
	case PSC_SCI_SNAP_PWAIT:
		if (PS_IsTxQueueEmpty()) {
			nGblSciState = PSC_SCI_SNAP_PAUSE;
		}
		break;
	default:
		break;
	}
	PSM_EnableIntr();
}

DefaultType	fGblVav = 0;
DefaultType	fGbltheta_V = 0;
DefaultType	fGblILd = 0;
DefaultType	fGblILq = 0;
DefaultType	fGblsIdc = 0;
DefaultType	fGblVcona = 0;
DefaultType	fGblVconb = 0;
DefaultType	fGblVconc = 0;
DefaultType	fGblUDELAY16 = 0;
DefaultType	fGblUDELAY17 = 0;
DefaultType	fGblVq = 0;
DefaultType	fGblVd = 0;
DefaultType	fGblVd1 = 0;
DefaultType	fGblVd2 = 0;
DefaultType	fGblUDELAY7 = 0;
DefaultType	fGblVcos = 0;
DefaultType	fGblVsin = 0;
DefaultType	fGblVs_sin = 0;
DefaultType	fGblVs_cos = 0;
DefaultType	fGblUDELAY9 = 0;
DefaultType	fGblVerr = 0;
DefaultType	fGblFreq = 0;
DefaultType	fGblVc = 0;
DefaultType	fGblUDELAY11 = 0;
DefaultType	fGblsVdc = 0;
DefaultType	fGblUDELAY4 = 0;
DefaultType	fGblIoqc1 = 0;
DefaultType	fGblVon = 0;
interrupt void Task()
{
	DefaultType	fZOH42, fZOH41, fUDELAY4, fMULT3, fUDELAY11, fP16, fUDELAY9;
	DefaultType	fSUMP18, fMULT4, fMULT14, fABC_AB1_1, fABC_AB1, fSIN_R2, fCOS_R2;
	DefaultType	fUDELAY7, fZOH49, fMULT12, fVSQ1, fNOT1, fLIM17, fLIM16, fLIM15;
	DefaultType	fZOH39, fUDELAY17, fMULT11, fUDELAY16, fMULT10, fAND4, fAND8;
	DefaultType	fAND31, fAND7, fCOMP8, fC25, fCOMP5, fC24, fAND6, fCOMP7, fC23;
	DefaultType	fCOMP4, fC22, fAND5, fCOMP6, fC21, fCOMP3, fC20, fCOMP2, fP38;
	DefaultType	fZOH40, fPSM_F28004x_ADC2_2, fC17, fF004x_DIN1, fS2_2, fS2_1;
	DefaultType	fS2, fDQO1_2, fDQO1_1, fDQO1, fLIM23, fDIVD2, fSUMP26, fSUM14;
	DefaultType	fP32, fLIM30, fSUMP16, fLIM26, fSUMP17, fP22, fP21, fSUM10;
	DefaultType	fLIM20, fP44, fSUMP15, fLIM2, fSUMP19, fP24, fP23, fSUM11, fFILTER_D3;
	DefaultType	fS4, fP30, fZOH36, fSUMP59, fPSM_F28004x_ADC1_8, fVDC8, fLIM22;
	DefaultType	fDIVD1, fC19, fSUMP25, fABC1_2, fABC1_1, fABC1, fSUMP27, fP29;
	DefaultType	fLIM31, fSUMP22, fLIM19, fSUMP23, fP28, fP27, fSUM13, fABC4_2;
	DefaultType	fABC4_1, fABC4, fI_RESET_I_D3, fSUMP5, fVDC7, fMULT15, fCOMP13;
	DefaultType	fSUMP30, fP42, fVDC10, fS3, fFCNM9, fABS3, fFCNM8, fABS2, fFCNM7;
	DefaultType	fP40, fZOH9, fSUMP52, fPSM_F28004x_ADC1, fABS1, fFCNM6, fP41;
	DefaultType	fZOH8, fSUMP51, fPSM_F28004x_ADC1_4, fP39, fZOH10, fSUMP53;
	DefaultType	fPSM_F28004x_ADC1_2, fLIM10, fSUMP6, fLIM11, fSUMP7, fP15, fP14;
	DefaultType	fP37, fZOH21, fSUMP57, fPSM_F28004x_ADC2_4, fP43, fZOH20, fSUMP56;
	DefaultType	fPSM_F28004x_ADC1_3, fP36, fZOH19, fSUMP55, fVDC14, fPSM_F28004x_ADC1_1;
	DefaultType	fC16;

	ADC_CLR(2) = 1 << (1-1);	// Clear ADC interrupt flag 1
	CPU_PIEACK |= M__INT1;
	fUDELAY16 = fGblUDELAY16;

	fUDELAY17 = fGblUDELAY17;

	fUDELAY7 = fGblUDELAY7;

	fUDELAY9 = fGblUDELAY9;

	fUDELAY11 = fGblUDELAY11;

	fUDELAY4 = fGblUDELAY4;


	fC16 = 0;
	fPSM_F28004x_ADC1_1 = ADC_RESULT(2, 1) * (1.0 * PSM_VRefHiC / 4096.0);
	fVDC14 = (-1.64);
	fSUMP55 = fPSM_F28004x_ADC1_1 + fVDC14;
	fZOH19 = fSUMP55;
	fP36 = fZOH19 * 3.788;
	fPSM_F28004x_ADC1_3 = ADC_RESULT(2, 3) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP56 = fPSM_F28004x_ADC1_3 + fVDC14;
	fZOH20 = fSUMP56;
	fP43 = fZOH20 * 3.788;
	fPSM_F28004x_ADC2_4 = ADC_RESULT(0, 1) * (1.0 * PSM_VRefHiA / 4096.0);
	fSUMP57 = fPSM_F28004x_ADC2_4 + fVDC14;
	fZOH21 = fSUMP57;
	fP37 = fZOH21 * 3.788;
	fP14 = fUDELAY9 * 50;
	fP15 = fUDELAY9 * 0.277;
	fSUMP7 = fP15 + fUDELAY7;
	fLIM11 = (fSUMP7 > 35) ? 35 : ((fSUMP7 < (-35)) ? (-35) : fSUMP7);
	fSUMP6 = fP14 + fLIM11;
	fLIM10 = (fSUMP6 > 35) ? 35 : ((fSUMP6 < (-35)) ? (-35) : fSUMP6);
	fPSM_F28004x_ADC1_2 = ADC_RESULT(2, 2) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP53 = fPSM_F28004x_ADC1_2 + fVDC14;
	fZOH10 = fSUMP53;
	fP39 = fZOH10 * 33.57;
	fPSM_F28004x_ADC1_4 = ADC_RESULT(2, 4) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP51 = fPSM_F28004x_ADC1_4 + fVDC14;
	fZOH8 = fSUMP51;
	fP41 = fZOH8 * 33.57;
	fFCNM6 = (fP39-fP41)/3.0;
	fABS1 = fabs(fFCNM6);
	fPSM_F28004x_ADC1 = ADC_RESULT(2, 0) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP52 = fPSM_F28004x_ADC1 + fVDC14;
	fZOH9 = fSUMP52;
	fP40 = fZOH9 * 33.57;
	fFCNM7 = (fP40-fP39)/3.0;
	fABS2 = fabs(fFCNM7);
	fFCNM8 = (fP41-fP40)/3.0;
	fABS3 = fabs(fFCNM8);
	fFCNM9 = (((fABS1+fABS2)+fABS3)/3.0)*100.0;
	{
		static	DefaultType fOutVal = 0.0;
		const DefaultType b0 = (1.0*1.0)/10.0E3/(1.0/(2*3.14159*50.0)+1.0/10.0E3);
		const DefaultType a1 = -1.0/(2*3.14159*50.0)/(1.0/(2*3.14159*50.0)+1.0/10.0E3);
		fS3 = b0 * fFCNM9 - a1 * fOutVal;
		fOutVal = fS3;
	}
#ifdef	_DEBUG
	fGblVav = fS3;
#endif

	fVDC10 = 20;
	fP42 = fUDELAY11 * 10;
	fSUMP30 = fVDC10 + fP42;
	fCOMP13 = (fS3 > fSUMP30) ? 1 : 0;
	fMULT15 = fLIM10 * fCOMP13;
	fVDC7 = 314;
	fSUMP5 = fMULT15 + fVDC7;
	{
		static DefaultType out_A = 0;
		static DefaultType in_A = 0.0;
		fI_RESET_I_D3 = out_A + 0.5/10000 * (fSUMP5 + in_A);
		if (fI_RESET_I_D3 > (2.0*3.1416)) {
			fI_RESET_I_D3 -= (2.0*3.1416) - 0;
		} else if (fI_RESET_I_D3 < 0) {
			fI_RESET_I_D3 += (2.0*3.1416) - 0;
		}
		out_A = fI_RESET_I_D3; in_A = fSUMP5;
	}
#ifdef	_DEBUG
	fGbltheta_V = fI_RESET_I_D3;
#endif

	// ABC to DQ transformation
	fABC4 = 2.0/3.0 * (cos(fI_RESET_I_D3) * fP36 + cos(fI_RESET_I_D3-2*3.14159265/3) * fP43 + cos(fI_RESET_I_D3+2*3.14159265/3) * fP37);
	fABC4_1 = 2.0/3.0 * (sin(fI_RESET_I_D3) * fP36 + sin(fI_RESET_I_D3-2*3.14159265/3) * fP43 + sin(fI_RESET_I_D3+2*3.14159265/3) * fP37);
	fABC4_2 = (fP36 + fP43 + fP37) / 3.0;
#ifdef	_DEBUG
	fGblILd = fABC4;
#endif

#ifdef	_DEBUG
	fGblILq = fABC4_1;
#endif

	fSUM13 = fC16 - fABC4;
	fP27 = fSUM13 * 2;
	fP28 = fSUM13 * 0.5;
	fSUMP23 = fP28 + fUDELAY17;
	fLIM19 = (fSUMP23 > 20) ? 20 : ((fSUMP23 < (-20)) ? (-20) : fSUMP23);
	fSUMP22 = fP27 + fLIM19;
	fLIM31 = (fSUMP22 > 20) ? 20 : ((fSUMP22 < (-20)) ? (-20) : fSUMP22);
	fP29 = fABC4_1 * (314.0*1.0E-3);
	fSUMP27 = fP29 - fLIM31;
	// ABC to DQ transformation
	fABC1 = 2.0/3.0 * (cos(fI_RESET_I_D3) * fFCNM6 + cos(fI_RESET_I_D3-2*3.14159265/3) * fFCNM7 + cos(fI_RESET_I_D3+2*3.14159265/3) * fFCNM8);
	fABC1_1 = 2.0/3.0 * (sin(fI_RESET_I_D3) * fFCNM6 + sin(fI_RESET_I_D3-2*3.14159265/3) * fFCNM7 + sin(fI_RESET_I_D3+2*3.14159265/3) * fFCNM8);
	fABC1_2 = (fFCNM6 + fFCNM7 + fFCNM8) / 3.0;
	fSUMP25 = fSUMP27 + fABC1;
	fC19 = 100;
	fDIVD1 = fSUMP25 / fC19;
	fLIM22 = (fDIVD1 > 1) ? 1 : ((fDIVD1 < (-1)) ? (-1) : fDIVD1);
	fVDC8 = 1.2;
	fPSM_F28004x_ADC1_8 = ADC_RESULT(2, 5) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP59 = fPSM_F28004x_ADC1_8 + fVDC14;
	fZOH36 = fSUMP59;
	fP30 = fZOH36 * 3.788;
	{
		static	DefaultType fOutVal = 0.0;
		const DefaultType b0 = (1.0*1.0)/10.0E3/(1.0/(2*3.14159*10.0)+1.0/10.0E3);
		const DefaultType a1 = -1.0/(2*3.14159*10.0)/(1.0/(2*3.14159*10.0)+1.0/10.0E3);
		fS4 = b0 * fP30 - a1 * fOutVal;
		fOutVal = fS4;
	}
#ifdef	_DEBUG
	fGblsIdc = fS4;
#endif

	{
		static DefaultType fIn = 0.0;
		static DefaultType fOut = 0.0;
		fFILTER_D3 = 0.0171539 * fS4 + 0.0171539 * fIn - (-0.9656922) * fOut;
		fIn = fS4;
		fOut = fFILTER_D3;
	}
	fSUM11 = fVDC8 - fFILTER_D3;
	fP23 = fSUM11 * 0.05;
	fP24 = fSUM11 * 0.01;
	fSUMP19 = fP24 + fUDELAY4;
	fLIM2 = (fSUMP19 > 8) ? 8 : ((fSUMP19 < (-8)) ? (-8) : fSUMP19);
	fSUMP15 = fP23 + fLIM2;
	fP44 = fSUMP15;
	fLIM20 = (fP44 > 10) ? 10 : ((fP44 < (-10)) ? (-10) : fP44);
	fSUM10 = fLIM20 - fABC4_1;
	fP21 = fSUM10 * 2;
	fP22 = fSUM10 * 0.5;
	fSUMP17 = fP22 + fUDELAY16;
	fLIM26 = (fSUMP17 > 20) ? 20 : ((fSUMP17 < (-20)) ? (-20) : fSUMP17);
	fSUMP16 = fP21 + fLIM26;
	fLIM30 = (fSUMP16 > 20) ? 20 : ((fSUMP16 < (-20)) ? (-20) : fSUMP16);
	fP32 = fABC4 * (314.0*1.0E-3);
	fSUM14 = fLIM30 * (-1) + fP32 * (-1);
	fSUMP26 = fSUM14 + fABC1_1;
	fDIVD2 = fSUMP26 / fC19;
	fLIM23 = (fDIVD2 > 1) ? 1 : ((fDIVD2 < (-1)) ? (-1) : fDIVD2);
	// DQ to ABC transformation
	fDQO1 = cos(fI_RESET_I_D3) * fLIM22 + sin(fI_RESET_I_D3) * fLIM23 + 0;
	fDQO1_1 = cos(fI_RESET_I_D3 - 2*3.14159265/3) * fLIM22 + sin(fI_RESET_I_D3 - 2*3.14159265/3) * fLIM23 + 0;
	fDQO1_2 = cos(fI_RESET_I_D3 + 2*3.14159265/3) * fLIM22 + sin(fI_RESET_I_D3 + 2*3.14159265/3) * fLIM23 + 0;
	{
		DefaultType	a, b, c, vMin, vMax, temp;
		a = fDQO1 * (1.0/1.7320508);
		b = fDQO1_1 * (1.0/1.7320508);
		c = fDQO1_2 * (1.0/1.7320508);
		if (a > b) {
			vMin = b;
			vMax = a;
		} else {
			vMin = a;
			vMax = b;
		}
		if (c > vMax)
			vMax = c;
		if (c < vMin)
			vMin = c;
		temp = vMin + vMax;
		fS2 = 2*a - temp;
		fS2_1 = 2*b - temp;
		fS2_2 = 2*c - temp;
	}
#ifdef	_DEBUG
	fGblVcona = fS2;
#endif

#ifdef	_DEBUG
	fGblVconb = fS2_1;
#endif

#ifdef	_DEBUG
	fGblVconc = fS2_2;
#endif

	fF004x_DIN1 = PSM_GpioGetInput(10);
	fC17 = 80;
	fPSM_F28004x_ADC2_2 = ADC_RESULT(0, 0) * (1.0 * PSM_VRefHiA / 4096.0);
	fZOH40 = fPSM_F28004x_ADC2_2;
	fP38 = fZOH40 * 33.57;
	fCOMP2 = (fC17 > fP38) ? 1 : 0;
	fC20 = 80;
	fCOMP3 = (fC20 > fP41) ? 1 : 0;
	fC21 = (-80);
	fCOMP6 = (fP41 > fC21) ? 1 : 0;
	fAND5 = (fCOMP3 > 0.3) && (fCOMP6 > 0.3);
	fC22 = 80;
	fCOMP4 = (fC22 > fP40) ? 1 : 0;
	fC23 = (-80);
	fCOMP7 = (fP40 > fC23) ? 1 : 0;
	fAND6 = (fCOMP4 > 0.3) && (fCOMP7 > 0.3);
	fC24 = 80;
	fCOMP5 = (fC24 > fP41) ? 1 : 0;
	fC25 = (-80);
	fCOMP8 = (fP41 > fC25) ? 1 : 0;
	fAND7 = (fCOMP5 > 0.3) && (fCOMP8 > 0.3);
	fAND31 = (fAND5 > 0.3) && (fAND6 > 0.3) && (fAND7 > 0.3);
	fAND8 = (fCOMP2 > 0.3) && (fAND31 > 0.3);
	fAND4 = (fF004x_DIN1 > 0.3) && (fAND8 > 0.3);
	fMULT10 = fLIM26 * fAND4;
	fGblUDELAY16 = fMULT10;
	fMULT11 = fLIM19 * fAND4;
	fGblUDELAY17 = fMULT11;
	fZOH39 = fS4;
	fLIM15 = (fS2 > 1) ? 1 : ((fS2 < (-1)) ? (-1) : fS2);
	fLIM16 = (fS2_1 > 1) ? 1 : ((fS2_1 < (-1)) ? (-1) : fS2_1);
	fLIM17 = (fS2_2 > 1) ? 1 : ((fS2_2 < (-1)) ? (-1) : fS2_2);
	if (fAND4 > 0.3)
	{
		PSM_Pwm3phStart(101-100);
	}
	fNOT1 = (fAND4 <= 0.3) ? 1 : 0;
	if (fNOT1 > 0.3)
	{
		PSM_Pwm3phStop(101-100);
	}
	{
		static DefaultType wt = 1.0 - (0 / 360.);
		static DefaultType dwt = 1 * 1.0 / 10000;
		fVSQ1 = (wt < 0.5) ? ((1) + (0)) : (0);
		wt += dwt;
		if (wt >= 1.0)
			wt -= 1.0;
	}
	fMULT12 = fVSQ1 * fAND4;
	fZOH49 = fMULT12;
	PSM_GpioSetOutput(6, fZOH49 > 0.3 ? 1 : 0);
#ifdef	_DEBUG
	fGblVq = fDIVD2;
#endif
#ifdef	_DEBUG
	fGblVd = fDIVD1;
#endif
#ifdef	_DEBUG
	fGblVd1 = fSUMP22;
#endif
#ifdef	_DEBUG
	fGblVd2 = fSUMP16;
#endif
	fGblUDELAY7 = fLIM11;
	fCOS_R2 = cos(fI_RESET_I_D3);
#ifdef	_DEBUG
	fGblVcos = fCOS_R2;
#endif
	fSIN_R2 = sin(fI_RESET_I_D3);
#ifdef	_DEBUG
	fGblVsin = fSIN_R2;
#endif
	// ABC to alpha/beta transformation
	fABC_AB1 = (fFCNM6 * 2 - (fFCNM7 + fFCNM8)) / 3.0;
	fABC_AB1_1 = 0.57735027 * (fFCNM7 - fFCNM8); // uvw2ab
#ifdef	_DEBUG
	fGblVs_sin = fABC_AB1;
#endif

#ifdef	_DEBUG
	fGblVs_cos = fABC_AB1_1;
#endif

	fMULT14 = fCOS_R2 * fABC_AB1;
	fMULT4 = fSIN_R2 * fABC_AB1_1;
	fSUMP18 = fMULT14 + fMULT4;
	fGblUDELAY9 = fSUMP18;
#ifdef	_DEBUG
	fGblVerr = fSUMP18;
#endif
	fP16 = fSUMP5 * (1.0/(2.0*3.1416));
#ifdef	_DEBUG
	fGblFreq = fP16;
#endif
#ifdef	_DEBUG
	fGblVc = fLIM10;
#endif
	fGblUDELAY11 = fCOMP13;
#ifdef	_DEBUG
	fGblsVdc = fP38;
#endif
	fMULT3 = fLIM2 * fAND4;
	fGblUDELAY4 = fMULT3;
#ifdef	_DEBUG
	fGblIoqc1 = fP44;
#endif
	fZOH41 = fI_RESET_I_D3;
	fZOH42 = fP16;
#ifdef	_DEBUG
	fGblVon = fCOMP13;
#endif
	// Start of changing PWM3ph1 registers
	// Set Duty Cycle of U
	PWM_CMPA(1) = PWM_TBPRD(1) * (__fsat(fLIM15, 1 + (-0.5), (-0.5)) - (-0.5)) * (1.0/1);
	PWM_CMPA(2) = PWM_TBPRD(1) * (__fsat(fLIM16, 1 + (-0.5), (-0.5)) - (-0.5)) * (1.0/1);
	PWM_CMPA(3) = PWM_TBPRD(1) * (__fsat(fLIM17, 1 + (-0.5), (-0.5)) - (-0.5)) * (1.0/1);
	// End of changing PWM3ph1 registers
	if (nGblSciState != PSC_SCI_INITIAL) {
		_ProcSciOutput(0, fZOH39);
		_ProcSciOutput(1, fZOH41);
		_ProcSciOutput(2, fZOH42);
	}
}

void Task_1()
{
	DefaultType	fVDC4, fVDC3;


	fVDC3 = 3.3;
	fVDC4 = 0;
	CMP_DACH(2) = __IQsat((int32)(fVDC3 * (4095.0/3.3) + 0.5), 4095, 0);
	CMP_DACL(2) = __IQsat((int32)(fVDC4 * (4095.0/3.3) + 0.5), 4095, 0);
	CMP_DACH(4) = __IQsat((int32)(fVDC3 * (4095.0/3.3) + 0.5), 4095, 0);
	CMP_DACL(4) = __IQsat((int32)(fVDC4 * (4095.0/3.3) + 0.5), 4095, 0);
}


void Initialize(void)
{
	PS_SysInit(2, 20);
	PS_PwmStartStopClock(0);	// Stop Pwm Clock
	PS_TimerInit(0, 0);

	// Set initial states for those GPIO output ports.
	PSM_GpioSetOutput(6, 0);	// Reset GPIO6
	PS_GpioSetFunc(6, 0, eSync1Samp, eGpioOutPullup, 0);	// Initialize GPIO6
	PS_SetVREF(0, 1, 0);	// Set external VRef for ADC-A
	PS_SetVREF(1, 1, 0);	// Set external VRef for ADC-B
	PS_SetVREF(2, 1, 0);	// Set external VRef for ADC-C

	{
	    int i, preAdcNo = -1;
	    /* PST_AdcAttr: Adc No., Channel No., Soc No., Trig Src, SampleTime(clock) */
	    const PST_AdcAttr aryAdcInit[8] = {
			{2, 0, 0, ADCTRIG_PWM1, 32},
			{2, 1, 1, ADCTRIG_PWM1, 32},
			{2, 2, 2, ADCTRIG_PWM1, 32},
			{2, 3, 3, ADCTRIG_PWM1, 32},
			{2, 4, 4, ADCTRIG_PWM1, 32},
			{2, 8, 5, ADCTRIG_PWM1, 32},
			{0, 2, 0, ADCTRIG_PWM1, 32},
			{0, 4, 1, ADCTRIG_PWM1, 32}};
	    const PST_AdcAttr *p = aryAdcInit;
	    for (i = 0; i < 8; i++, p++) {
	        if (preAdcNo != p->nAdcNo) {
	            PS_AdcInit(p->nAdcNo);
	            preAdcNo = p->nAdcNo;
	        }
	        PS_AdcSetChn(p->nAdcNo, p->nChnNo, p->nSocNo, p->nTrigSrc, p->nWindSz);
	    }
	}

	// Set PWM X-BAR TRIP4
	PS_PwmXBarSetSig(4, 2, 1);		// Use PWM X-BAR MUX2
	PS_PwmXBarInv(4, 0);

	// Set PWM X-BAR TRIP5
	PS_PwmXBarSetSig(5, 6, 1);		// Use PWM X-BAR MUX6
	PS_PwmXBarInv(5, 0);

	PS_Pwm3phInit(1, 0, 1, 1.e6/(10000*2.0), ePwmStartHigh1, ePwmComplement, HRPWM_DISABLE);	// pwmNo, seqNo, wave type, period, PwmA, PWMB, UseHRPwm
	PS_PwmSetDeadBand(1, 0, 2, 3, 0, 1, 1);
	PS_PwmSetDeadBand(2, 0, 2, 3, 0, 1, 1);
	PS_PwmSetDeadBand(3, 0, 2, 3, 0, 1, 1);
	PS_PwmSetIntrType(1, ePwmIntrAdc, 2, 0);
	PS_AdcSetIntr(2, 1, 5, Task); // AdcNo, IntrNo, SocNo, Interrupt Vector
	PS_PwmSetTripAction(1, eTzForceLow, eTzForceLow);
	PS_PwmSetTripAction(2, eTzForceLow, eTzForceLow);
	PS_PwmSetTripAction(3, eTzForceLow, eTzForceLow);
	PWM_CMPA(1) = (0 - (-0.5)) / (1.0 * 1) * PWM_TBPRD(1);
	PWM_CMPA(2) = (0 - (-0.5)) / (1.0 * 1) * PWM_TBPRD(1);
	PWM_CMPA(3) = (0 - (-0.5)) / (1.0 * 1) * PWM_TBPRD(1);
	PSM_Pwm3phStop(1);       // Stop Pwm3ph1 at the beginning

	// Initialization for PWM3ph1 DC tripzone
	PS_PwmSetDcEvent(1, 1, 3, -1, eTzEvtDCxHhigh); // Set sole DCAH/L event (cycle by cycle) to PWM1
	PS_PwmSetDcEvent(2, 1, 3, -1, eTzEvtDCxHhigh); // Set sole DCAH/L event (cycle by cycle) to the 2nd PWM
	PS_PwmSetDcEvent(3, 1, 3, -1, eTzEvtDCxHhigh); // Set sole DCAH/L event (cycle by cycle) to the 3rd PWM
	PS_PwmSetDcEvent(1, 3, 4, -1, eTzEvtDCxHhigh); // Set sole DCBH/L event (cycle-by-cycle) to PWM1
	PS_PwmSetDcEvent(2, 3, 4, -1, eTzEvtDCxHhigh); // Set sole DCBH/L event (cycle-by-cycle) to the 2nd PWM
	PS_PwmSetDcEvent(3, 3, 4, -1, eTzEvtDCxHhigh); // Set sole DCBH/L event (cycle-by-cycle) to the 3rd PWM
	PS_PwmSetDcTrip(1, 10);
	PS_PwmSetDcTrip(2, 10);
	PS_PwmSetDcTrip(3, 10);

	PS_GpioSetFunc(10, 0, eSync1Samp, eGpioIn, 0);

	PS_GpioSetFunc(6, 0, eSync1Samp, eGpioOutPullup, 0);

	PS_AsysIntrcnctCnfg(2, 0, 1);
	PS_AsysIntrcnctCnfg(2, 3, 1);
	PS_CompInit(2, 1, 0, 0 * (_ERR_ * _ERR_ * 1.0e-6), -1);
		// Sync. with PWM1
	PS_CompUseDac(2, 0);	//Choose VDDA(3.3V) as Comp2 DAC voltage reference
	PS_CompSideInit(2, 0, 0, 0, 3);		// High side compares to COMPDACH
	PS_CompSideInit(2, 1, 0, 1, 3);	// Low side compares to COMPDACL

	PS_AsysIntrcnctCnfg(4, 0, 1);
	PS_AsysIntrcnctCnfg(4, 3, 1);
	PS_CompInit(4, 1, 0, 0 * (_ERR_ * _ERR_ * 1.0e-6), -1);
		// Sync. with PWM1
	PS_CompUseDac(4, 0);	//Choose VDDA(3.3V) as Comp4 DAC voltage reference
	PS_CompSideInit(4, 0, 0, 0, 3);		// High side compares to COMPDACH
	PS_CompSideInit(4, 1, 0, 1, 3);	// Low side compares to COMPDACL

	PS_SciInit(13, 12, 115200, 0, aGblSciOutBuf, 32, &_ProcSciInputItem);	// Rx(GPIO13), Tx(GPIO12)

	PS_PwmStartStopClock(1);	// Start Pwm Clock
}


void main()
{
	Initialize();
	PSM_EnableIntr();   // Enable Global interrupt INTM
	PSM_EnableDbgm();
	for (;;) {
		_ProcSciWaitStart();
		Task_1();
	}
}

